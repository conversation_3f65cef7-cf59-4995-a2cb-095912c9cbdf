import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAdminAuthContext } from 'context/adminAuth'
import { AdminLayout } from 'components/Admin/Layout'

interface BookRating {
  id: string
  userId: string
  userName: string
  userEmail: string
  bookId: string
  bookTitle: string
  rating: number
  comment: string
  isApproved: boolean
  createdAt: string
  updatedAt: string
}

// Mock data for ratings
const mockRatings: BookRating[] = [
  {
    id: '1',
    userId: '1',
    userName: 'Nguyễn Văn A',
    userEmail: '<EMAIL>',
    bookId: '1',
    bookTitle: 'IB Mathematics Analysis and Approaches HL',
    rating: 5,
    comment:
      '<PERSON><PERSON><PERSON> rất hay, giải thích chi tiết và dễ hiểu. Rất phù hợp cho học sinh IB.',
    isApproved: true,
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:30:00Z',
  },
  {
    id: '2',
    userId: '2',
    userName: 'Trần Thị B',
    userEmail: '<EMAIL>',
    bookId: '2',
    bookTitle: 'Cambridge IELTS 18 Academic',
    rating: 4,
    comment:
      'Đề thi chuẩn Cambridge, giúp ôn luyện hiệu quả. Tuy nhiên giá hơi cao.',
    isApproved: true,
    createdAt: '2024-01-19T10:15:00Z',
    updatedAt: '2024-01-19T10:15:00Z',
  },
  {
    id: '3',
    userId: '3',
    userName: 'Lê Văn C',
    userEmail: '<EMAIL>',
    bookId: '1',
    bookTitle: 'IB Mathematics Analysis and Approaches HL',
    rating: 3,
    comment: 'Nội dung tốt nhưng in ấn không rõ ràng lắm.',
    isApproved: false,
    createdAt: '2024-01-18T16:45:00Z',
    updatedAt: '2024-01-18T16:45:00Z',
  },
  {
    id: '4',
    userId: '4',
    userName: 'Phạm Thị D',
    userEmail: '<EMAIL>',
    bookId: '3',
    bookTitle: 'IB Physics Course Book',
    rating: 5,
    comment:
      'Excellent book! Very comprehensive coverage of IB Physics topics.',
    isApproved: true,
    createdAt: '2024-01-17T09:20:00Z',
    updatedAt: '2024-01-17T09:20:00Z',
  },
  {
    id: '5',
    userId: '5',
    userName: 'Hoàng Văn E',
    userEmail: '<EMAIL>',
    bookId: '2',
    bookTitle: 'Cambridge IELTS 18 Academic',
    rating: 2,
    comment: 'Sách này không có gì đặc biệt, giống như các phiên bản trước.',
    isApproved: false,
    createdAt: '2024-01-16T13:10:00Z',
    updatedAt: '2024-01-16T13:10:00Z',
  },
]

export default function AdminRatings() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [ratings, setRatings] = useState<BookRating[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/login')
      return
    }

    // Mock API call
    setTimeout(() => {
      setRatings(mockRatings)
      setIsLoading(false)
    }, 500)
  }, [isAdminLogin, router])

  const filteredRatings = ratings.filter((rating) => {
    const matchesSearch =
      rating.bookTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rating.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      rating.comment.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus =
      statusFilter === '' ||
      (statusFilter === 'approved' && rating.isApproved) ||
      (statusFilter === 'pending' && !rating.isApproved)
    return matchesSearch && matchesStatus
  })

  const handleApprove = (ratingId: string) => {
    setRatings((prev) =>
      prev.map((rating) =>
        rating.id === ratingId ? { ...rating, isApproved: true } : rating,
      ),
    )
  }

  const handleReject = (ratingId: string) => {
    setRatings((prev) =>
      prev.map((rating) =>
        rating.id === ratingId ? { ...rating, isApproved: false } : rating,
      ),
    )
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span
        key={i}
        className={i < rating ? 'text-yellow-400' : 'text-gray-300'}
      >
        ⭐
      </span>
    ))
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">
              Quản lý Đánh giá
            </h1>
            <p className="mt-2 text-sm text-gray-700">
              Danh sách đánh giá của khách hàng về các sách
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <input
              type="text"
              placeholder="Tìm kiếm theo sách, người dùng, nội dung..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            />
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="approved">Đã duyệt</option>
              <option value="pending">Chờ duyệt</option>
            </select>
          </div>
        </div>

        {/* Ratings Table */}
        <div className="mt-8 flex flex-col">
          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Người dùng
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sách
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Đánh giá
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Nội dung
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredRatings.map((rating) => (
                      <tr key={rating.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {rating.userName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {rating.userEmail}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-xs truncate">
                            {rating.bookTitle}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {renderStars(rating.rating)}
                            <span className="ml-2 text-sm text-gray-600">
                              ({rating.rating}/5)
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-xs">
                            {rating.comment.length > 100
                              ? `${rating.comment.substring(0, 100)}...`
                              : rating.comment}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              rating.isApproved
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}
                          >
                            {rating.isApproved ? 'Đã duyệt' : 'Chờ duyệt'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          {!rating.isApproved ? (
                            <button
                              onClick={() => handleApprove(rating.id)}
                              className="text-green-600 hover:text-green-900 mr-4"
                            >
                              Duyệt
                            </button>
                          ) : (
                            <button
                              onClick={() => handleReject(rating.id)}
                              className="text-yellow-600 hover:text-yellow-900 mr-4"
                            >
                              Hủy duyệt
                            </button>
                          )}
                          <button className="text-red-600 hover:text-red-900">
                            Xóa
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
