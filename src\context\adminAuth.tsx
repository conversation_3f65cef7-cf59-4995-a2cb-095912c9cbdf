import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react'

export interface AdminUser {
  id: string
  email: string
  name: string
  role: 'admin' | 'super_admin'
}

interface AdminAuthContextType {
  adminUser: AdminUser | null
  isAdminLogin: boolean
  adminLogin: (email: string, password: string) => Promise<boolean>
  adminLogout: () => void
}

const AdminAuthContext = createContext<AdminAuthContextType | undefined>(
  undefined,
)

interface AdminAuthProviderProps {
  children: ReactNode
}

export const AdminAuthProvider = ({ children }: AdminAuthProviderProps) => {
  const [adminUser, setAdminUser] = useState<AdminUser | null>(null)
  const [isAdminLogin, setIsAdminLogin] = useState(false)

  // Check for existing admin session on mount
  useEffect(() => {
    if (typeof window === 'undefined') return

    const adminToken = localStorage.getItem('admin_token')
    const storedAdminUser = localStorage.getItem('admin_user')

    if (adminToken && storedAdminUser) {
      try {
        const user = JSON.parse(storedAdminUser) as AdminUser
        setAdminUser(user)
        setIsAdminLogin(true)
      } catch (error) {
        // Clear invalid data
        localStorage.removeItem('admin_token')
        localStorage.removeItem('admin_user')
      }
    }
  }, [])

  const adminLogin = async (
    email: string,
    _password: string,
  ): Promise<boolean> => {
    try {
      // Mock admin login - accept any email/password for now
      const mockUser: AdminUser = {
        id: '1',
        email: email,
        name: 'Admin User',
        role: 'admin',
      }

      const mockToken = 'mock-admin-token-' + Date.now()

      setAdminUser(mockUser)
      setIsAdminLogin(true)

      // Store in localStorage
      localStorage.setItem('admin_token', mockToken)
      localStorage.setItem('admin_user', JSON.stringify(mockUser))

      return true
    } catch (error) {
      console.error('Admin login error:', error)
      return false
    }
  }

  const adminLogout = () => {
    setAdminUser(null)
    setIsAdminLogin(false)
    localStorage.removeItem('admin_token')
    localStorage.removeItem('admin_user')
  }

  const value: AdminAuthContextType = {
    adminUser,
    isAdminLogin,
    adminLogin,
    adminLogout,
  }

  return (
    <AdminAuthContext.Provider value={value}>
      {children}
    </AdminAuthContext.Provider>
  )
}

export const useAdminAuthContext = () => {
  const context = useContext(AdminAuthContext)
  if (context === undefined) {
    throw new Error(
      'useAdminAuthContext must be used within an AdminAuthProvider',
    )
  }
  return context
}
