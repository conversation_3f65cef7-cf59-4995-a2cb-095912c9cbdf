import Link from 'next/link'

import { useCartContext } from 'context/cart'
import { mockBooks } from 'data/books'
import { formatNumber } from 'utils/number'

interface ProductCardProps {
  id: string
  title: string
  image: string
  price: number
  originalPrice?: number
  rating: number
  reviewCount: number
  isNew?: boolean
  category: string
  slug: string
}

export const ProductCard = ({
  id,
  title,
  image,
  price,
  originalPrice,
  rating,
  reviewCount,
  isNew,
  category,
  slug,
}: ProductCardProps) => {
  const { addToCart } = useCartContext()
  const discountPercent = originalPrice
    ? Math.round(((originalPrice - price) / originalPrice) * 100)
    : 0

  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Find the book from mockBooks
    const book = mockBooks.find((b) => b.id === id)
    if (book) {
      addToCart(book)
    }
  }

  return (
    <div className="group bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl hover:border-primary-200 transition-all duration-300 transform hover:-translate-y-1">
      <Link href={`/product/${slug}`}>
        <div className="relative">
          {/* Product Image */}
          <div className="aspect-[3/4] bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
            <img
              src={image}
              alt={title}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
            />
            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>

          {/* Badges */}
          <div className="absolute top-4 left-4 flex flex-col gap-2 z-10">
            {isNew && (
              <span className="bg-gradient-to-r from-green-500 to-green-600 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm">
                Mới
              </span>
            )}
            {discountPercent > 0 && (
              <span className="bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-semibold px-3 py-1.5 rounded-full shadow-lg backdrop-blur-sm">
                -{discountPercent}%
              </span>
            )}
          </div>

          {/* Quick Add Button */}
          <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 z-10">
            <button
              onClick={handleAddToCart}
              className="bg-white hover:bg-primary-50 text-primary-600 hover:text-primary-700 p-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-200 border border-primary-200 backdrop-blur-sm"
              title="Thêm vào giỏ hàng"
            >
              <svg
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
                />
              </svg>
            </button>
          </div>
        </div>

        {/* Product Info */}
        <div className="p-5">
          {/* Category */}
          <div className="text-xs text-primary-600 mb-2 uppercase tracking-wider font-semibold">
            {category}
          </div>

          {/* Title */}
          <h3 className="font-semibold text-gray-900 mb-3 line-clamp-2 text-sm leading-tight hover:text-primary-600 transition-colors duration-200">
            {title}
          </h3>

          {/* Rating */}
          <div className="flex items-center mb-3">
            <div className="flex items-center">
              {[...Array(5)].map((_, i) => (
                <svg
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'
                  }`}
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
              ))}
            </div>
            <span className="text-xs text-gray-600 ml-2 font-medium">
              {rating.toFixed(1)} ({reviewCount} đánh giá)
            </span>
          </div>

          {/* Price */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <span className="text-xl font-bold text-primary-600">
                {formatNumber(price)} ₫
              </span>
              {originalPrice && (
                <span className="text-sm text-gray-500 line-through">
                  {formatNumber(originalPrice)} ₫
                </span>
              )}
            </div>
          </div>

          {/* Add to Cart Button */}
          <button
            onClick={handleAddToCart}
            className="w-full bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white py-3 px-4 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105 shadow-md hover:shadow-lg"
          >
            Thêm vào giỏ
          </button>
        </div>
      </Link>
    </div>
  )
}
