import { useState } from 'react'
import Link from 'next/link'

import { BookStoreLayout } from 'components/BookStore/Layout'
import { useCartContext } from 'context/cart'
import { formatNumber } from 'utils/number'
import { Button } from 'components/Button'

export default function CartPage() {
  const {
    items,
    totalItems,
    totalPrice,
    updateQuantity,
    removeFromCart,
    clearCart,
  } = useCartContext()
  const [isLoading, setIsLoading] = useState(false)

  const handleQuantityChange = (bookId: string, newQuantity: number) => {
    if (newQuantity < 1) return
    updateQuantity(bookId, newQuantity)
  }

  const handleRemoveItem = (bookId: string) => {
    removeFromCart(bookId)
  }

  const handleClearCart = () => {
    if (window.confirm('Bạn có chắc chắn muốn xóa tất cả sản phẩm?')) {
      clearCart()
    }
  }

  const handleCheckout = () => {
    setIsLoading(true)
    // Redirect to checkout page
    window.location.href = '/checkout'
  }

  if (items.length === 0) {
    return (
      <BookStoreLayout>
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center">
            <div className="w-32 h-32 mx-auto mb-8 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-lg">
              <svg
                className="w-16 h-16 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
                />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 mb-6">
              Giỏ hàng trống
            </h1>
            <p className="text-xl text-gray-600 mb-10 max-w-md mx-auto leading-relaxed">
              Hãy thêm sách vào giỏ hàng để tiếp tục mua sắm
            </p>
            <Link href="/bookstore">
              <Button
                size="lg"
                className="px-8 py-4 text-lg font-semibold transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                Tiếp tục mua sắm
              </Button>
            </Link>
          </div>
        </div>
      </BookStoreLayout>
    )
  }

  return (
    <BookStoreLayout>
      <div className="max-w-6xl mx-auto px-4 py-12">
        {/* Header */}
        <div className="flex items-center justify-between mb-12">
          <div>
            <h1 className="text-4xl font-bold text-gray-900 mb-2">Giỏ hàng</h1>
            <p className="text-lg text-gray-600">{totalItems} sản phẩm</p>
          </div>
          <button
            onClick={handleClearCart}
            className="bg-red-50 hover:bg-red-100 text-red-600 hover:text-red-700 font-semibold px-6 py-3 rounded-lg transition-all duration-200 border border-red-200 hover:border-red-300"
          >
            Xóa tất cả
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-lg shadow-sm border">
              {items.map((item, index) => (
                <div
                  key={item.book.id}
                  className={`p-6 ${
                    index !== items.length - 1 ? 'border-b' : ''
                  }`}
                >
                  <div className="flex items-start space-x-4">
                    {/* Product Image */}
                    <div className="flex-shrink-0">
                      <img
                        src={item.book.image}
                        alt={item.book.title}
                        className="w-20 h-24 object-cover rounded"
                      />
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-medium text-gray-900 mb-2">
                        <Link
                          href={`/product/${item.book.slug}`}
                          className="hover:text-primary-600"
                        >
                          {item.book.title}
                        </Link>
                      </h3>
                      <p className="text-sm text-gray-600 mb-2">
                        {item.book.publisher} • {item.book.category}
                      </p>
                      <div className="flex items-center space-x-4">
                        <span className="text-lg font-bold text-primary-600">
                          {formatNumber(item.book.price)} ₫
                        </span>
                        {item.book.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {formatNumber(item.book.originalPrice)} ₫
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Quantity Controls */}
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() =>
                          handleQuantityChange(item.book.id, item.quantity - 1)
                        }
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                        disabled={item.quantity <= 1}
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M20 12H4"
                          />
                        </svg>
                      </button>
                      <span className="w-8 text-center font-medium">
                        {item.quantity}
                      </span>
                      <button
                        onClick={() =>
                          handleQuantityChange(item.book.id, item.quantity + 1)
                        }
                        className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50"
                      >
                        <svg
                          className="w-4 h-4"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                          />
                        </svg>
                      </button>
                    </div>

                    {/* Remove Button */}
                    <button
                      onClick={() => handleRemoveItem(item.book.id)}
                      className="text-red-600 hover:text-red-700 p-2"
                    >
                      <svg
                        className="w-5 h-5"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-gradient-to-br from-white to-gray-50 rounded-xl shadow-lg border border-gray-200 p-8 sticky top-4">
              <h2 className="text-2xl font-bold text-gray-900 mb-8">
                Tóm tắt đơn hàng
              </h2>

              <div className="space-y-6 mb-8">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 font-medium">Tạm tính</span>
                  <span className="font-semibold text-lg">
                    {formatNumber(totalPrice)} ₫
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600 font-medium">
                    Phí vận chuyển
                  </span>
                  <span className="font-semibold text-green-600 bg-green-50 px-3 py-1 rounded-full text-sm">
                    Miễn phí
                  </span>
                </div>
                <div className="border-t-2 border-gray-200 pt-6">
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-gray-900">
                      Tổng cộng
                    </span>
                    <span className="text-2xl font-bold text-primary-600">
                      {formatNumber(totalPrice)} ₫
                    </span>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleCheckout}
                size="lg"
                className="w-full mb-6 py-4 text-lg font-bold bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center justify-center">
                    <svg
                      className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                    >
                      <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                      ></circle>
                      <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                      ></path>
                    </svg>
                    Đang xử lý...
                  </div>
                ) : (
                  'Tiến hành thanh toán'
                )}
              </Button>

              <div className="text-center mb-6">
                <Link
                  href="/bookstore"
                  className="inline-flex items-center text-primary-600 hover:text-primary-700 font-semibold transition-colors duration-200"
                >
                  <svg
                    className="w-4 h-4 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                  Tiếp tục mua sắm
                </Link>
              </div>

              <div className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg
                      className="w-6 h-6 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h4 className="text-sm font-bold text-blue-900 mb-2">
                      Cần hỗ trợ?
                    </h4>
                    <p className="text-sm text-blue-800 leading-relaxed">
                      Liên hệ: <span className="font-semibold">0908540198</span>
                      <br />
                      Email:{' '}
                      <span className="font-semibold"><EMAIL></span>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </BookStoreLayout>
  )
}
