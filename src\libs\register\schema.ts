import * as yup from 'yup'

export const registerSchema = yup.object({
  name: yup
    .string()
    .required('<PERSON><PERSON> và tên là bắt buộc')
    .min(2, 'Họ và tên phải có ít nhất 2 ký tự'),
  email: yup.string().required('<PERSON>ail là bắt buộc').email('Email không hợp lệ'),
  phone: yup
    .string()
    .required('Số điện thoại là bắt buộc')
    .matches(/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ'),
  password: yup
    .string()
    .required('Mật khẩu là bắt buộc')
    .min(6, 'Mật khẩu phải có ít nhất 6 ký tự')
    .matches(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 1 chữ hoa, 1 chữ thường và 1 số',
    ),
  confirmPassword: yup
    .string()
    .required('<PERSON><PERSON>c nhận mật khẩu là bắt buộc')
    .oneOf([yup.ref('password')], 'Mật khẩu xác nhận không khớp'),
})

export type RegisterFormData = yup.InferType<typeof registerSchema>
