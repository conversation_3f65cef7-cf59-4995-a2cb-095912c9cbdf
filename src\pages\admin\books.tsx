import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAdminAuthContext } from 'context/adminAuth'
import { AdminLayout } from 'components/Admin/Layout'

interface Book {
  id: string
  title: string
  description: string
  price: number
  categoryId: string
  categoryName: string
  programName: string
  author: string
  publisher: string
  isbn: string
  imageUrl: string
  isActive: boolean
  stock: number
  rating: number
  reviewCount: number
  createdAt: string
  updatedAt: string
}

// Mock data for books
const mockBooks: Book[] = [
  {
    id: '1',
    title: 'IB Mathematics Analysis and Approaches HL',
    description:
      'Comprehensive textbook for IB Mathematics Analysis and Approaches Higher Level',
    price: 450000,
    categoryId: '1',
    categoryName: 'Analysis and Approaches',
    programName: 'IB Mathematics',
    author: '<PERSON>, <PERSON><PERSON><PERSON>',
    publisher: 'Cambridge University Press',
    isbn: '978-1108440066',
    imageUrl: '/images/books/ib-math-aa-hl.jpg',
    isActive: true,
    stock: 25,
    rating: 4.5,
    reviewCount: 12,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    title: 'Cambridge IELTS 18 Academic',
    description: 'Official IELTS practice materials with authentic test papers',
    price: 320000,
    categoryId: '4',
    categoryName: 'Listening',
    programName: 'IELTS Preparation',
    author: 'Cambridge Assessment English',
    publisher: 'Cambridge University Press',
    isbn: '978-1316637456',
    imageUrl: '/images/books/cambridge-ielts-18.jpg',
    isActive: true,
    stock: 50,
    rating: 4.8,
    reviewCount: 28,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '3',
    title: 'IB Physics Course Book',
    description: 'Complete coverage of the IB Physics syllabus',
    price: 520000,
    categoryId: '3',
    categoryName: 'Mechanics',
    programName: 'IB Physics',
    author: 'Michael Bowen-Jones, David Homer',
    publisher: 'Oxford University Press',
    isbn: '978-0198307754',
    imageUrl: '/images/books/ib-physics.jpg',
    isActive: true,
    stock: 15,
    rating: 4.3,
    reviewCount: 8,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
]

export default function AdminBooks() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [books, setBooks] = useState<Book[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/login')
      return
    }

    // Mock API call
    setTimeout(() => {
      setBooks(mockBooks)
      setIsLoading(false)
    }, 500)
  }, [isAdminLogin, router])

  const filteredBooks = books.filter((book) => {
    const matchesSearch =
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.isbn.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory =
      selectedCategory === '' || book.categoryId === selectedCategory
    return matchesSearch && matchesCategory
  })

  const categories = Array.from(
    new Set(
      books.map((b) => ({
        id: b.categoryId,
        name: b.categoryName,
        program: b.programName,
      })),
    ),
  )

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">
              Quản lý Sách
            </h1>
            <p className="mt-2 text-sm text-gray-700">
              Danh sách tất cả sách trong hệ thống
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button
              type="button"
              className="inline-flex items-center justify-center rounded-md border border-transparent bg-primary-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 sm:w-auto"
            >
              Thêm sách
            </button>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-2">
          <div>
            <input
              type="text"
              placeholder="Tìm kiếm sách, tác giả, ISBN..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            />
          </div>
          <div>
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">Tất cả loại sách</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name} ({category.program})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Books Table */}
        <div className="mt-8 flex flex-col">
          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Sách
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Loại / Chương trình
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Giá
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Kho / Đánh giá
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredBooks.map((book) => (
                      <tr key={book.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-16 w-12">
                              <img
                                className="h-16 w-12 object-cover rounded"
                                src={book.imageUrl}
                                alt={book.title}
                                onError={(e) => {
                                  e.currentTarget.src =
                                    '/images/book-placeholder.jpg'
                                }}
                              />
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {book.title}
                              </div>
                              <div className="text-sm text-gray-500">
                                {book.author} • {book.publisher}
                              </div>
                              <div className="text-xs text-gray-400">
                                ISBN: {book.isbn}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {book.categoryName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {book.programName}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatPrice(book.price)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          <div>
                            <div>Kho: {book.stock}</div>
                            <div>
                              ⭐ {book.rating} ({book.reviewCount})
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              book.isActive
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}
                          >
                            {book.isActive ? 'Hoạt động' : 'Tạm dừng'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-primary-600 hover:text-primary-900 mr-4">
                            Sửa
                          </button>
                          <button className="text-red-600 hover:text-red-900">
                            Xóa
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
