import { UserInfo } from '../types/auth'

const USER_KEY = 'userInfo'
const ACCESS_TOKEN_KEY = 'accessToken'
const REFRESH_TOKEN_KEY = 'refreshToken'

export const localStorageUtils = {
  getUser: (): UserInfo | null => {
    if (typeof window === 'undefined') return null
    const storedUser = localStorage.getItem(USER_KEY)

    return storedUser ? (JSON.parse(storedUser) as UserInfo) : null
  },

  setUser(user: UserInfo | null): void {
    if (typeof window === 'undefined') return
    if (user) {
      localStorage.setItem(USER_KEY, JSON.stringify(user))
    } else {
      localStorage.removeItem(USER_KEY)
    }
  },

  removeUser: (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem(USER_KEY)
  },

  getAccessToken: (): string | null => {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(ACCESS_TOKEN_KEY)
  },

  setAccessToken: (token: string): void => {
    if (typeof window === 'undefined') return
    localStorage.setItem(ACCESS_TOKEN_KEY, token)
  },

  removeAccessToken: (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem(ACCESS_TOKEN_KEY)
  },

  clearAll: (): void => {
    if (typeof window === 'undefined') return
    localStorage.removeItem(USER_KEY)
    localStorage.removeItem(ACCESS_TOKEN_KEY)
    localStorage.removeItem(REFRESH_TOKEN_KEY)
  },
}
