import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useState } from 'react'
import { useAuth } from '@/context/authContext'
import { toast } from '@/components/Toast'
import { loginSchema, LoginFormData } from './schema'

export const useLoginForm = () => {
  const { login, isLoading } = useAuth()
  const [showPassword, setShowPassword] = useState(false)

  const form = useForm<LoginFormData>({
    resolver: yupResolver(loginSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  })

  const onSubmit = async (data: LoginFormData) => {
    try {
      await login(data)
      toast.success({
        title: 'Đăng nhập thành công!',
        message: 'Chào mừng bạn quay trở lại',
      })
    } catch (error) {
      toast.error({
        title: 'Đăng nhập thất bại',
        message:
          error instanceof Error
            ? error.message
            : 'Vui lòng kiểm tra lại thông tin đăng nhập',
      })
    }
  }

  return {
    form,
    onSubmit,
    showPassword,
    setShowPassword,
    isLoading,
  }
}
