import { useState, useEffect } from 'react'

import { useRouter } from 'next/router'
import { useAdminAuthContext } from 'context/adminAuth'
import { AdminLayout } from 'components/Admin/Layout'

import { Dialog } from 'components/Dialog'
import { formatNumber } from 'utils/number'

interface Order {
  id: string
  customerName: string
  email: string
  phone: string
  address: string
  city: string
  district: string
  ward: string
  totalPrice: number
  status:
    | 'pending'
    | 'processing'
    | 'shipped'
    | 'delivered'
    | 'cancelled'
    | 'pending_payment'
    | 'paid'
    | 'payment_failed'
  paymentMethod: 'direct' | 'online'
  createdAt: string
  updatedAt: string
  items: Array<{
    bookId: string
    title: string
    price: number
    quantity: number
  }>
  notes?: string
}

// Mock data - replace with API call
const mockOrders: Order[] = [
  {
    id: 'ORD1001',
    customerName: 'Nguyễn Văn A',
    email: '<EMAIL>',
    phone: '0901234567',
    address: '123 Đường ABC',
    city: 'Hà Nội',
    district: '<PERSON><PERSON><PERSON>',
    ward: 'Dịch Vọng',
    totalPrice: 450000,
    status: 'pending',
    paymentMethod: 'direct',
    createdAt: '2024-07-20T10:30:00Z',
    updatedAt: '2024-07-20T10:30:00Z',
    items: [
      {
        bookId: '1',
        title: "Our World level 4 Student's Book",
        price: 170000,
        quantity: 2,
      },
      {
        bookId: '4',
        title: 'English File Pre-intermediate Workbook',
        price: 40000,
        quantity: 2,
      },
    ],
    notes: 'Giao hàng buổi chiều',
  },
  {
    id: 'ORD1002',
    customerName: 'Trần Thị B',
    email: '<EMAIL>',
    phone: '0912345678',
    address: '456 Đường XYZ',
    city: 'TP.HCM',
    district: 'Quận 1',
    ward: 'Bến Nghé',
    totalPrice: 320000,
    status: 'processing',
    paymentMethod: 'online',
    createdAt: '2024-07-19T14:20:00Z',
    updatedAt: '2024-07-19T15:00:00Z',
    items: [
      {
        bookId: '2',
        title: 'Level Up Maths Levels 3-5 Pupil Book',
        price: 260000,
        quantity: 1,
      },
      {
        bookId: '5',
        title: 'Macmillan CAE Testbuilder',
        price: 60000,
        quantity: 1,
      },
    ],
  },
]

export default function AdminOrdersPage() {
  const { adminUser } = useAdminAuthContext()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>(mockOrders)
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null)
  const [showOrderDetail, setShowOrderDetail] = useState(false)

  useEffect(() => {
    if (!adminUser) {
      router.push('/login')
    } else {
      fetchOrders()
    }
  }, [adminUser, router, selectedStatus])

  const fetchOrders = async () => {
    try {
      // Mock orders data
      const mockOrders = [
        {
          id: 'ORD-001',
          customerName: 'Nguyễn Văn A',
          email: '<EMAIL>',
          phone: '0901234567',
          address: '123 Đường ABC',
          city: 'TP.HCM',
          district: 'Quận 1',
          ward: 'Phường 1',
          paymentMethod: 'direct' as const,
          status: 'pending' as const,
          totalPrice: 450000,
          items: [
            {
              bookId: '1',
              title: 'Cambridge English First',
              price: 450000,
              quantity: 1,
            },
          ],
          notes: 'Giao hàng buổi sáng',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
        {
          id: 'ORD-002',
          customerName: 'Trần Thị B',
          email: '<EMAIL>',
          phone: '0902345678',
          address: '456 Đường XYZ',
          city: 'TP.HCM',
          district: 'Quận 2',
          ward: 'Phường 2',
          paymentMethod: 'online' as const,
          status: 'processing' as const,
          totalPrice: 680000,
          items: [
            {
              bookId: '2',
              title: 'IELTS Cambridge 15',
              price: 340000,
              quantity: 2,
            },
          ],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      setOrders(mockOrders)
    } catch (error) {
      console.error('Error fetching orders:', error)
    }
  }

  const updateOrderStatus = async (orderId: string, newStatus: string) => {
    // Update local state immediately for better UX
    setOrders((prevOrders) =>
      prevOrders.map((order) =>
        order.id === orderId
          ? {
              ...order,
              status: newStatus as Order['status'],
              updatedAt: new Date().toISOString(),
            }
          : order,
      ),
    )

    try {
      const { orderService } = await import('services/api')
      await orderService.updateStatus(orderId, newStatus)

      // Update selected order if it's the same
      if (selectedOrder && selectedOrder.id === orderId) {
        setSelectedOrder({
          ...selectedOrder,
          status: newStatus as Order['status'],
        })
      }
    } catch (error) {
      console.error('Error updating order status:', error)
      // Revert on error
      fetchOrders()
    }
  }

  const viewOrderDetail = (order: Order) => {
    setSelectedOrder(order)
    setShowOrderDetail(true)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'processing':
        return 'bg-blue-100 text-blue-800'
      case 'shipped':
        return 'bg-purple-100 text-purple-800'
      case 'delivered':
        return 'bg-green-100 text-green-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      case 'pending_payment':
        return 'bg-orange-100 text-orange-800'
      case 'paid':
        return 'bg-emerald-100 text-emerald-800'
      case 'payment_failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Chờ xử lý'
      case 'processing':
        return 'Đang xử lý'
      case 'shipped':
        return 'Đã gửi'
      case 'delivered':
        return 'Đã giao'
      case 'cancelled':
        return 'Đã hủy'
      case 'pending_payment':
        return 'Chờ thanh toán'
      case 'paid':
        return 'Đã thanh toán'
      case 'payment_failed':
        return 'Thanh toán thất bại'
      default:
        return status
    }
  }

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus =
      selectedStatus === 'all' || order.status === selectedStatus

    return matchesSearch && matchesStatus
  })

  if (!adminUser) {
    return <div>Loading...</div>
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">
            Quản lý đơn hàng
          </h1>
          <p className="mt-2 text-sm text-gray-700">
            Theo dõi và xử lý đơn hàng
          </p>
        </div>

        {/* Filters */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm
              </label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Tìm theo tên, email, mã đơn hàng..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trạng thái
              </label>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="all">Tất cả trạng thái</option>
                <option value="pending">Chờ xử lý</option>
                <option value="processing">Đang xử lý</option>
                <option value="shipped">Đã gửi</option>
                <option value="delivered">Đã giao</option>
                <option value="cancelled">Đã hủy</option>
                <option value="pending_payment">Chờ thanh toán</option>
                <option value="paid">Đã thanh toán</option>
                <option value="payment_failed">Thanh toán thất bại</option>
              </select>
            </div>
            <div className="flex items-end">
              <div className="text-sm text-gray-600">
                Hiển thị {filteredOrders.length} / {orders.length} đơn hàng
              </div>
            </div>
          </div>
        </div>

        {/* Orders Table */}
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Mã đơn hàng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Khách hàng
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ngày đặt
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Tổng tiền
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trạng thái
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Thao tác
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredOrders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{order.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {order.customerName}
                      </div>
                      <div className="text-sm text-gray-500">{order.email}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(order.createdAt).toLocaleDateString('vi-VN')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatNumber(order.totalPrice)} ₫
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(
                          order.status,
                        )}`}
                      >
                        {getStatusText(order.status)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                      <button
                        onClick={() => viewOrderDetail(order)}
                        className="text-primary-600 hover:text-primary-900"
                      >
                        Xem
                      </button>
                      <select
                        value={order.status}
                        onChange={(e) =>
                          updateOrderStatus(order.id, e.target.value)
                        }
                        className="text-sm border border-gray-300 rounded px-2 py-1"
                      >
                        <option value="pending">Chờ xử lý</option>
                        <option value="processing">Đang xử lý</option>
                        <option value="shipped">Đã gửi</option>
                        <option value="delivered">Đã giao</option>
                        <option value="cancelled">Đã hủy</option>
                        <option value="pending_payment">Chờ thanh toán</option>
                        <option value="paid">Đã thanh toán</option>
                        <option value="payment_failed">
                          Thanh toán thất bại
                        </option>
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Order Detail Dialog */}
        {selectedOrder && (
          <Dialog
            isOpen={showOrderDetail}
            onClose={() => setShowOrderDetail(false)}
            title={`Chi tiết đơn hàng #${selectedOrder.id}`}
            maxWidth="2xl"
          >
            <div className="space-y-6">
              {/* Customer Info */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Thông tin khách hàng
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Họ tên:</span>
                    <span className="ml-2 font-medium">
                      {selectedOrder.customerName}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Email:</span>
                    <span className="ml-2 font-medium">
                      {selectedOrder.email}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Điện thoại:</span>
                    <span className="ml-2 font-medium">
                      {selectedOrder.phone}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">
                      Phương thức thanh toán:
                    </span>
                    <span className="ml-2 font-medium">
                      {selectedOrder.paymentMethod === 'direct'
                        ? 'Thanh toán trực tiếp'
                        : 'Thanh toán online'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Shipping Address */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Địa chỉ giao hàng
                </h3>
                <div className="text-sm text-gray-600">
                  <p>{selectedOrder.address}</p>
                  <p>
                    {selectedOrder.ward}, {selectedOrder.district},{' '}
                    {selectedOrder.city}
                  </p>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">
                  Sản phẩm đã đặt
                </h3>
                <div className="space-y-3">
                  {selectedOrder.items.map((item, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">
                          {item.title}
                        </h4>
                        <p className="text-sm text-gray-600">
                          Số lượng: {item.quantity}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">
                          {formatNumber(item.price * item.quantity)} ₫
                        </p>
                        <p className="text-sm text-gray-600">
                          {formatNumber(item.price)} ₫ × {item.quantity}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Summary */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center text-lg font-semibold">
                  <span>Tổng cộng:</span>
                  <span className="text-primary-600">
                    {formatNumber(selectedOrder.totalPrice)} ₫
                  </span>
                </div>
              </div>

              {/* Notes */}
              {selectedOrder.notes && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Ghi chú
                  </h3>
                  <p className="text-sm text-gray-600">{selectedOrder.notes}</p>
                </div>
              )}

              {/* Order Dates */}
              <div className="text-sm text-gray-600 space-y-1">
                <p>
                  Ngày đặt:{' '}
                  {new Date(selectedOrder.createdAt).toLocaleString('vi-VN')}
                </p>
                <p>
                  Cập nhật lần cuối:{' '}
                  {new Date(selectedOrder.updatedAt).toLocaleString('vi-VN')}
                </p>
              </div>
            </div>
          </Dialog>
        )}
      </div>
    </AdminLayout>
  )
}
