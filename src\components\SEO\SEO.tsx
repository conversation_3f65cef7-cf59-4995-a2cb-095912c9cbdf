import Head from 'next/head'
import { useRouter } from 'next/router'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  image?: string
  noindex?: boolean
  canonical?: string
}

export const SEO = ({
  title,
  description,
  keywords,
  image = '/og-image.jpg',
  noindex = false,
  canonical,
}: SEOProps) => {
  const router = useRouter()

  const siteTitle = 'IBBook'
  const siteDescription =
    'IBBook - Chuyên cung cấp sách tiếng Anh Cambridge, IELTS, Oxford, Pearson với giá tốt nhất. Giao hàng toàn quốc, tặng kèm file nghe miễn phí.'

  const pageTitle = title ? `${title} | ${siteTitle}` : siteTitle
  const pageDescription = description || siteDescription
  const pageKeywords =
    keywords ||
    'sách tiếng anh, cambridge, ielts, oxford, pearson, english books hanoi'

  const currentUrl = `https://sachtienganhhanoi.com${router.asPath}`
  const canonicalUrl = canonical || currentUrl

  // Generate alternate language URLs
  const alternateUrls =
    router.locales?.map((locale) => ({
      locale,
      url: `https://sachtienganhhanoi.com${
        locale === 'vi' ? '' : `/${locale}`
      }${router.asPath.replace(/^\/[a-z]{2}/, '')}`,
    })) || []

  return (
    <Head>
      {/* Basic Meta Tags */}
      <title>{pageTitle}</title>
      <meta name="description" content={pageDescription} />
      <meta name="keywords" content={pageKeywords} />
      <meta name="author" content="Sách tiếng Anh Hà Nội" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />

      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />

      {/* Language Alternates */}
      {alternateUrls.map(({ locale, url }) => (
        <link key={locale} rel="alternate" hrefLang={locale} href={url} />
      ))}

      {/* Open Graph */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={pageDescription} />
      <meta
        property="og:image"
        content={`https://sachtienganhhanoi.com${image}`}
      />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:site_name" content={siteTitle} />
      <meta
        property="og:locale"
        content={router.locale === 'vi' ? 'vi_VN' : 'en_US'}
      />

      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={pageDescription} />
      <meta
        name="twitter:image"
        content={`https://sachtienganhhanoi.com${image}`}
      />

      {/* Favicon */}
      <link rel="icon" href="/favicon.ico" />
      <link
        rel="apple-touch-icon"
        sizes="180x180"
        href="/apple-touch-icon.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="32x32"
        href="/favicon-32x32.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="16x16"
        href="/favicon-16x16.png"
      />

      {/* Robots */}
      {noindex && <meta name="robots" content="noindex,nofollow" />}

      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BookStore',
            name: siteTitle,
            description: pageDescription,
            url: currentUrl,
            logo: 'https://sachtienganhhanoi.com/logo.png',
            address: {
              '@type': 'PostalAddress',
              addressCountry: 'VN',
              addressLocality: 'Hà Nội',
            },
            contactPoint: {
              '@type': 'ContactPoint',
              telephone: '+84949351612',
              contactType: 'customer service',
            },
            sameAs: [
              'https://www.facebook.com/sachtienganhhanoi',
              'https://zalo.me/sachtienganhhanoi',
            ],
          }),
        }}
      />
    </Head>
  )
}
