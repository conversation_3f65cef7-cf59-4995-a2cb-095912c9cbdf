import '../styles/index.css'
import React from 'react'
import App from 'next/app'

import Head from 'next/head'
import { AdminAuthProvider } from 'context/adminAuth'
import { AuthProvider } from 'context/authContext'
import { CartProvider } from 'context/cart'
import { Toaster } from 'components/Toast'

class MyApp extends App {
  render() {
    const { Component, pageProps } = this.props

    return (
      <>
        <Head>
          <meta content="IE=edge" httpEquiv="X-UA-Compatible" />
          <meta content="width=device-width, initial-scale=1" name="viewport" />
          <title>IBBook - Chuyên sách tiếng Anh</title>
          <meta content="IBBook - Chuyên sách tiếng Anh" property="og:title" />
          <meta content="@sachtienganhhanoi" name="twitter:site" />
          <meta content="summary_large_image" name="twitter:card" />
          <meta
            content="IBBook - Chuyên cung cấp sách tiếng <PERSON>, IELTS, Oxford, <PERSON> với giá tốt nhất. Giao hàng toàn quốc, tặng kèm file nghe miễn phí."
            name="description"
          />
          <meta
            content="IBBook - Chuyên cung cấp sách tiếng Anh Cambridge, IELTS, Oxford, Pearson với giá tốt nhất. Giao hàng toàn quốc, tặng kèm file nghe miễn phí."
            property="og:description"
          />
          <meta content="/thumbnail.jpeg" property="og:image" />
          <meta content="/thumbnail.jpeg" name="twitter:image" />
        </Head>
        <AuthProvider>
          <AdminAuthProvider>
            <CartProvider>
              <Component {...pageProps} />
            </CartProvider>
          </AdminAuthProvider>
        </AuthProvider>
        <Toaster />
      </>
    )
  }
}
export default MyApp
