import { useState } from 'react'

import { ProductCard } from '../ProductCard'
import { mockBooks } from 'data/books'

export const ProductGrid = () => {
  const [sortBy, setSortBy] = useState('default')
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 10

  // Sort books based on selected option
  const sortedBooks = [...mockBooks].sort((a, b) => {
    switch (sortBy) {
      case 'price-low':
        return a.price - b.price
      case 'price-high':
        return b.price - a.price
      case 'rating':
        return b.rating - a.rating
      case 'newest':
        return b.isNew ? 1 : -1
      default:
        return 0
    }
  })

  // Pagination
  const totalPages = Math.ceil(sortedBooks.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentBooks = sortedBooks.slice(startIndex, endIndex)

  return (
    <div>
      {/* Header with sorting */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Tất cả sách</h1>
          <p className="text-gray-600">
            Hiển thị {startIndex + 1}–{Math.min(endIndex, sortedBooks.length)}{' '}
            trong {sortedBooks.length} kết quả
          </p>
        </div>

        <div className="flex items-center space-x-4">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          >
            <option value="default">Mặc định</option>
            <option value="popularity">Phổ biến</option>
            <option value="rating">Đánh giá cao</option>
            <option value="newest">Mới nhất</option>
            <option value="price-low">Giá thấp đến cao</option>
            <option value="price-high">Giá cao đến thấp</option>
          </select>
        </div>
      </div>

      {/* Product Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
        {currentBooks.map((book) => (
          <ProductCard
            key={book.id}
            id={book.id}
            title={book.title}
            image={book.image}
            price={book.price}
            originalPrice={book.originalPrice}
            rating={book.rating}
            reviewCount={book.reviewCount}
            isNew={book.isNew}
            category={book.category}
            slug={book.slug}
          />
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center mt-12 space-x-2">
          {/* Previous button */}
          <button
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            ←
          </button>

          {/* Page numbers */}
          {[...Array(Math.min(7, totalPages))].map((_, index) => {
            let pageNumber: number
            if (totalPages <= 7) {
              pageNumber = index + 1
            } else if (currentPage <= 4) {
              pageNumber = index + 1
            } else if (currentPage >= totalPages - 3) {
              pageNumber = totalPages - 6 + index
            } else {
              pageNumber = currentPage - 3 + index
            }

            return (
              <button
                key={pageNumber}
                onClick={() => setCurrentPage(pageNumber)}
                className={`px-3 py-2 text-sm border rounded-lg ${
                  currentPage === pageNumber
                    ? 'bg-primary-500 text-white border-primary-500'
                    : 'border-gray-300 hover:bg-gray-50'
                }`}
              >
                {pageNumber}
              </button>
            )
          })}

          {/* Show dots if there are more pages */}
          {totalPages > 7 && currentPage < totalPages - 3 && (
            <>
              <span className="px-2 py-2 text-sm text-gray-500">…</span>
              <button
                onClick={() => setCurrentPage(totalPages)}
                className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {totalPages}
              </button>
            </>
          )}

          {/* Next button */}
          <button
            onClick={() =>
              setCurrentPage(Math.min(totalPages, currentPage + 1))
            }
            disabled={currentPage === totalPages}
            className="px-3 py-2 text-sm border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            →
          </button>
        </div>
      )}
    </div>
  )
}
