import { useForm } from 'react-hook-form'
import { yupResolver } from '@hookform/resolvers/yup'
import { useState } from 'react'
import { useAuth } from '@/context/authContext'
import { toast } from '@/components/Toast'
import { registerSchema, RegisterFormData } from './schema'

export const useRegisterForm = () => {
  const { register: registerUser, isLoading } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const form = useForm<RegisterFormData>({
    resolver: yupResolver(registerSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    },
  })

  const onSubmit = async (data: RegisterFormData) => {
    try {
      await registerUser(data)
      toast.success({
        title: 'Đăng ký thành công!',
        message: '<PERSON><PERSON><PERSON> khoản của bạn đã được tạo thành công',
      })
    } catch (error) {
      toast.error({
        title: 'Đăng ký thất bại',
        message:
          error instanceof Error
            ? error.message
            : 'Vui lòng kiểm tra lại thông tin đăng ký',
      })
    }
  }

  return {
    form,
    onSubmit,
    showPassword,
    setShowPassword,
    showConfirmPassword,
    setShowConfirmPassword,
    isLoading,
  }
}
