{"name": "nextjs-boilerplate", "version": "1.1.0", "scripts": {"dev": "next dev", "build": "next build", "analyze": "ANALYZE=true pnpm build", "start": "next start", "format": "prettier -c --write \"*/**\"", "lint": "eslint . --ext .ts,.tsx,.js", "lint:fix": "eslint . --ext .ts,.tsx,.js --fix", "release": "standard-version -a", "preinstall": "npx only-allow pnpm", "prepare": "husky", "clean:install": "rimraf node_modules pnpm-lock.yaml && pnpm install"}, "dependencies": {"@babel/eslint-parser": "^7.22.10", "@hookform/resolvers": "^3.10.0", "@next/bundle-analyzer": "^14.2.5", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@tailwindcss/forms": "^0.5.7", "classnames": "^2.5.1", "date-fns": "^4.1.0", "isomorphic-unfetch": "^4.0.2", "lucide-react": "^0.525.0", "next": "^15.3.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-hot-toast": "^2.4.1", "recharts": "^3.1.0", "swr": "^2.2.5", "tailwind-variants": "^0.2.1", "yup": "^1.6.1", "zod": "^3.25.76"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@total-typescript/ts-reset": "^0.5.1", "@types/node": "22.1.0", "@types/nprogress": "^0.2.3", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@typescript-eslint/eslint-plugin": "6.4.0", "@typescript-eslint/parser": "6.4.0", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "dotenv": "^16.4.5", "eslint": "8.30.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-next": "^13.1.1", "eslint-config-prettier": "9.0.0", "eslint-config-react": "1.x", "eslint-config-react-app": "^7.0.1", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.6.1", "eslint-plugin-prettier": "5.0.0", "eslint-plugin-react": "7.31.11", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-storybook": "^0.6.13", "husky": "^9.1.4", "lint-staged": "^15.2.8", "postcss": "^8.4.41", "prettier": "^2.8.1", "rimraf": "^6.0.1", "standard-version": "^9.5.0", "stylelint": "^15.10.2", "stylelint-config-prettier": "^9.0.4", "stylelint-config-standard": "^34.0.0", "tailwindcss": "3.4.9", "tailwindcss-animate": "^1.0.7", "tsconfig-paths-webpack-plugin": "^4.1.0", "typescript": "5.1.6"}, "license": "ISC", "lint-staged": {"**/*.{json,md}": ["prettier --write"], "**/*.{js,jsx,ts,tsx}": ["prettier --write", "eslint --fix"]}, "engines": {"pnpm": ">=8.2.0", "node": ">=20.0.0"}}