import { ReactNode } from 'react'
import { <PERSON><PERSON> } from './Header'
import { Footer } from './Footer'

interface BookStoreLayoutProps {
  children: ReactNode
}

export const BookStoreLayout = ({ children }: BookStoreLayoutProps) => {
  return (
    <div className="min-h-screen bg-white">
      {/* <SEO title={title} description={description} keywords={keywords} /> */}
      <Header />
      <main className="min-h-screen">{children}</main>
      <Footer />
    </div>
  )
}
