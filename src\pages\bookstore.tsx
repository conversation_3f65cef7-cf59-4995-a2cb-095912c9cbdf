import { BookStoreLayout } from 'components/BookStore/Layout'
import { Hero } from 'components/BookStore/Hero'
import { ProductGrid } from 'components/BookStore/ProductGrid'
import { Sidebar } from 'components/BookStore/Sidebar'

export default function BookStorePage() {
  return (
    <BookStoreLayout>
      {/* Hero Section */}
      <Hero />

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <aside className="lg:w-80 flex-shrink-0">
            <div className="sticky top-4">
              <Sidebar />
            </div>
          </aside>

          {/* Main Product Grid */}
          <main className="flex-1">
            <ProductGrid />
          </main>
        </div>
      </div>
    </BookStoreLayout>
  )
}
