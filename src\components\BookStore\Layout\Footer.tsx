import Link from 'next/link'

export const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Company Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Thông tin</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/about" className="hover:text-primary-300">
                  • Sách Spiral
                </Link>
              </li>
              <li>
                <Link href="/blog" className="hover:text-primary-300">
                  • Blog
                </Link>
              </li>
              <li>
                <Link href="/shipping" className="hover:text-primary-300">
                  • Giao hàng
                </Link>
              </li>
              <li>
                <Link href="/guide" className="hover:text-primary-300">
                  • Hướng dẫn
                </Link>
              </li>
              <li>
                <Link href="/discount" className="hover:text-primary-300">
                  • Khuy<PERSON>n mãi
                </Link>
              </li>
              <li>
                <Link href="/points" className="hover:text-primary-300">
                  • Điểm thưởng
                </Link>
              </li>
              <li>
                <Link href="/return" className="hover:text-primary-300">
                  • Đổi trả
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories 1 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Danh mục sách</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/early-years" className="hover:text-primary-300">
                  • Mầm non
                </Link>
              </li>
              <li>
                <Link href="/primary" className="hover:text-primary-300">
                  • Tiểu học
                </Link>
              </li>
              <li>
                <Link href="/secondary" className="hover:text-primary-300">
                  • Trung học
                </Link>
              </li>
              <li>
                <Link href="/adults" className="hover:text-primary-300">
                  • Người lớn
                </Link>
              </li>
              <li>
                <Link href="/esp" className="hover:text-primary-300">
                  • ESP
                </Link>
              </li>
              <li>
                <Link href="/iq-books" className="hover:text-primary-300">
                  • IQ Books
                </Link>
              </li>
              <li>
                <Link href="/french" className="hover:text-primary-300">
                  • Tiếng Pháp
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories 2 */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Chương trình học</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/study-abroad" className="hover:text-primary-300">
                  • Du học
                </Link>
              </li>
              <li>
                <Link href="/asean" className="hover:text-primary-300">
                  • ASEAN
                </Link>
              </li>
              <li>
                <Link href="/grammar" className="hover:text-primary-300">
                  • Ngữ pháp
                </Link>
              </li>
              <li>
                <Link href="/exam-prep" className="hover:text-primary-300">
                  • Luyện thi
                </Link>
              </li>
              <li>
                <Link href="/ib" className="hover:text-primary-300">
                  • IB
                </Link>
              </li>
              <li>
                <Link href="/a-level" className="hover:text-primary-300">
                  • A-Level
                </Link>
              </li>
              <li>
                <Link href="/emg" className="hover:text-primary-300">
                  • EMG
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact & Social */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Liên hệ</h3>
            <div className="space-y-4">
              <p className="text-sm">
                Liên hệ với chúng tôi để được hỗ trợ tốt nhất
              </p>
              <div className="flex items-center space-x-4">
                <Link
                  href="tel:0949351612"
                  className="bg-primary-500 hover:bg-primary-600 px-4 py-2 rounded text-sm font-medium"
                >
                  0949351612
                </Link>
                <Link
                  href="/zalo"
                  className="bg-blue-500 hover:bg-blue-600 px-4 py-2 rounded text-sm font-medium"
                >
                  Zalo
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-8 pt-8 text-center text-sm text-gray-400">
          <p>© 2024 IBBook. Tất cả quyền được bảo lưu.</p>
        </div>
      </div>
    </footer>
  )
}
