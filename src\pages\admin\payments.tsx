import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import { useAdminAuthContext } from 'context/adminAuth'
import { AdminLayout } from 'components/Admin/Layout'

interface Payment {
  id: string
  orderId: string
  userId: string
  userName: string
  userEmail: string
  amount: number
  method: 'direct' | 'online' | '9pay' | 'momo' | 'vnpay'
  status: 'pending' | 'completed' | 'failed' | 'refunded'
  transactionId?: string
  gatewayResponse?: string
  createdAt: string
  updatedAt: string
}

// Mock data for payments
const mockPayments: Payment[] = [
  {
    id: '1',
    orderId: 'ORD-001',
    userId: '1',
    userName: 'Nguyễn <PERSON>n <PERSON>',
    userEmail: '<EMAIL>',
    amount: 450000,
    method: '9pay',
    status: 'completed',
    transactionId: '9PAY-TXN-123456',
    gatewayResponse: 'Payment successful',
    createdAt: '2024-01-20T14:30:00Z',
    updatedAt: '2024-01-20T14:35:00Z',
  },
  {
    id: '2',
    orderId: 'ORD-002',
    userId: '2',
    userName: 'Trần Thị B',
    userEmail: '<EMAIL>',
    amount: 680000,
    method: 'direct',
    status: 'pending',
    createdAt: '2024-01-20T10:15:00Z',
    updatedAt: '2024-01-20T10:15:00Z',
  },
  {
    id: '3',
    orderId: 'ORD-003',
    userId: '3',
    userName: 'Lê Văn C',
    userEmail: '<EMAIL>',
    amount: 320000,
    method: 'momo',
    status: 'failed',
    transactionId: 'MOMO-TXN-789012',
    gatewayResponse: 'Insufficient balance',
    createdAt: '2024-01-19T16:45:00Z',
    updatedAt: '2024-01-19T16:50:00Z',
  },
  {
    id: '4',
    orderId: 'ORD-004',
    userId: '4',
    userName: 'Phạm Thị D',
    userEmail: '<EMAIL>',
    amount: 520000,
    method: 'vnpay',
    status: 'completed',
    transactionId: 'VNP-TXN-345678',
    gatewayResponse: 'Transaction completed successfully',
    createdAt: '2024-01-19T09:20:00Z',
    updatedAt: '2024-01-19T09:25:00Z',
  },
  {
    id: '5',
    orderId: 'ORD-005',
    userId: '5',
    userName: 'Hoàng Văn E',
    userEmail: '<EMAIL>',
    amount: 290000,
    method: 'online',
    status: 'refunded',
    transactionId: 'REF-901234',
    gatewayResponse: 'Refund processed',
    createdAt: '2024-01-18T13:10:00Z',
    updatedAt: '2024-01-18T15:30:00Z',
  },
]

export default function AdminPayments() {
  const { isAdminLogin } = useAdminAuthContext()
  const router = useRouter()
  const [payments, setPayments] = useState<Payment[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [methodFilter, setMethodFilter] = useState('')

  useEffect(() => {
    if (typeof window !== 'undefined' && !isAdminLogin) {
      router.push('/admin/login')
      return
    }

    // Mock API call
    setTimeout(() => {
      setPayments(mockPayments)
      setIsLoading(false)
    }, 500)
  }, [isAdminLogin, router])

  const filteredPayments = payments.filter((payment) => {
    const matchesSearch =
      payment.orderId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.userName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.transactionId
        ?.toLowerCase()
        .includes(searchTerm.toLowerCase() || '')
    const matchesStatus = statusFilter === '' || payment.status === statusFilter
    const matchesMethod = methodFilter === '' || payment.method === methodFilter
    return matchesSearch && matchesStatus && matchesMethod
  })

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      case 'refunded':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return 'Thành công'
      case 'pending':
        return 'Chờ xử lý'
      case 'failed':
        return 'Thất bại'
      case 'refunded':
        return 'Đã hoàn tiền'
      default:
        return status
    }
  }

  const getMethodText = (method: string) => {
    switch (method) {
      case 'direct':
        return 'Trực tiếp'
      case 'online':
        return 'Online'
      case '9pay':
        return '9Pay'
      case 'momo':
        return 'MoMo'
      case 'vnpay':
        return 'VNPay'
      default:
        return method
    }
  }

  if (isLoading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">
              Quản lý Thanh toán
            </h1>
            <p className="mt-2 text-sm text-gray-700">
              Danh sách các giao dịch thanh toán trong hệ thống
            </p>
          </div>
        </div>

        {/* Filters */}
        <div className="mt-6 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div>
            <input
              type="text"
              placeholder="Tìm kiếm theo mã đơn, khách hàng, mã giao dịch..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            />
          </div>
          <div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">Tất cả trạng thái</option>
              <option value="completed">Thành công</option>
              <option value="pending">Chờ xử lý</option>
              <option value="failed">Thất bại</option>
              <option value="refunded">Đã hoàn tiền</option>
            </select>
          </div>
          <div>
            <select
              value={methodFilter}
              onChange={(e) => setMethodFilter(e.target.value)}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
            >
              <option value="">Tất cả phương thức</option>
              <option value="direct">Trực tiếp</option>
              <option value="9pay">9Pay</option>
              <option value="momo">MoMo</option>
              <option value="vnpay">VNPay</option>
              <option value="online">Online khác</option>
            </select>
          </div>
        </div>

        {/* Payments Table */}
        <div className="mt-8 flex flex-col">
          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Đơn hàng / Khách hàng
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Số tiền
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Phương thức
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Mã giao dịch
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Trạng thái
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Thời gian
                      </th>
                      <th className="relative px-6 py-3">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredPayments.map((payment) => (
                      <tr key={payment.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {payment.orderId}
                            </div>
                            <div className="text-sm text-gray-500">
                              {payment.userName}
                            </div>
                            <div className="text-xs text-gray-400">
                              {payment.userEmail}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {formatPrice(payment.amount)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            {getMethodText(payment.method)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {payment.transactionId || '-'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(
                              payment.status,
                            )}`}
                          >
                            {getStatusText(payment.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(payment.createdAt).toLocaleDateString(
                            'vi-VN',
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button className="text-primary-600 hover:text-primary-900 mr-4">
                            Chi tiết
                          </button>
                          {payment.status === 'completed' && (
                            <button className="text-red-600 hover:text-red-900">
                              Hoàn tiền
                            </button>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  )
}
