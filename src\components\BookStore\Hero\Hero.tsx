import Link from 'next/link'

export const Hero = () => {
  return (
    <div className="relative bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div
          className="w-full h-full bg-repeat"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}
        ></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-white to-primary-100 bg-clip-text text-transparent">
            IBBook - Cửa hàng sách tiếng Anh
          </h1>
          <p className="text-xl md:text-2xl mb-12 text-primary-100 max-w-3xl mx-auto leading-relaxed">
            Khám phá thế giới sách tiếng Anh chất lượng cao từ các nhà xuất bản
            hàng đầu
          </p>

          {/* Featured Categories */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-16">
            <Link
              href="/cambridge"
              className="group bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl p-8 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl border border-white/20"
            >
              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center group-hover:from-white/40 group-hover:to-white/20 transition-all duration-300">
                  <span className="text-3xl font-bold">C</span>
                </div>
                <h3 className="font-bold text-lg mb-2">Cambridge</h3>
                <p className="text-sm text-primary-100 leading-relaxed">
                  Sách giáo khoa Cambridge chính hãng
                </p>
              </div>
            </Link>

            <Link
              href="/ielts"
              className="group bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl p-8 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl border border-white/20"
            >
              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center group-hover:from-white/40 group-hover:to-white/20 transition-all duration-300">
                  <span className="text-3xl font-bold">I</span>
                </div>
                <h3 className="font-bold text-lg mb-2">IELTS</h3>
                <p className="text-sm text-primary-100 leading-relaxed">
                  Tài liệu luyện thi IELTS hiệu quả
                </p>
              </div>
            </Link>

            <Link
              href="/skills"
              className="group bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl p-8 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl border border-white/20"
            >
              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center group-hover:from-white/40 group-hover:to-white/20 transition-all duration-300">
                  <span className="text-3xl font-bold">S</span>
                </div>
                <h3 className="font-bold text-lg mb-2">Kỹ năng</h3>
                <p className="text-sm text-primary-100 leading-relaxed">
                  Phát triển 4 kỹ năng tiếng Anh
                </p>
              </div>
            </Link>

            <Link
              href="/exam"
              className="group bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-xl p-8 transition-all duration-300 transform hover:scale-105 hover:shadow-2xl border border-white/20"
            >
              <div className="text-center">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-white/30 to-white/10 rounded-full flex items-center justify-center group-hover:from-white/40 group-hover:to-white/20 transition-all duration-300">
                  <span className="text-3xl font-bold">E</span>
                </div>
                <h3 className="font-bold text-lg mb-2">Luyện thi</h3>
                <p className="text-sm text-primary-100 leading-relaxed">
                  Chuẩn bị cho các kỳ thi quốc tế
                </p>
              </div>
            </Link>
          </div>

          {/* Grade Level Selector */}
          <div className="mt-12">
            <h2 className="text-2xl font-semibold mb-6">
              Shop by Grade or CERF level:
            </h2>
            <div className="grid grid-cols-4 md:grid-cols-9 gap-3">
              {['Pre-K', 'K', '1', '2', '3', '4', '5', '6', '7', '8'].map(
                (grade) => (
                  <Link
                    key={grade}
                    href={`/grade/${grade.toLowerCase()}`}
                    className="bg-white/10 hover:bg-white/20 rounded-lg p-4 transition-colors"
                  >
                    <div className="text-center">
                      <div className="text-lg font-bold">Grade {grade}</div>
                    </div>
                  </Link>
                ),
              )}
            </div>

            <div className="grid grid-cols-4 md:grid-cols-8 gap-3 mt-4">
              {[
                'Starters',
                'Movers',
                'Flyers',
                'A2 (KET)',
                'B1 (PET)',
                'B2 (FCE)',
                'C1 (CAE)',
                'C2 (CPE)',
              ].map((level) => (
                <Link
                  key={level}
                  href={`/level/${level
                    .toLowerCase()
                    .replace(/[^a-z0-9]/g, '-')}`}
                  className="bg-white/10 hover:bg-white/20 rounded-lg p-4 transition-colors"
                >
                  <div className="text-center">
                    <div className="text-sm font-bold">{level}</div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
