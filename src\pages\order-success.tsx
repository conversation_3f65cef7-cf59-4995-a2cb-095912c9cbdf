import { useEffect, useState } from 'react'

import Link from 'next/link'
import { GetServerSideProps } from 'next'

import { BookStoreLayout } from 'components/BookStore/Layout'
import { Button } from 'components/Button'
import { formatNumber } from 'utils/number'

interface OrderSuccessProps {
  orderId?: string
}

interface Order {
  id: string
  customerName: string
  email: string
  phone: string
  address: string
  city: string
  district: string
  ward: string
  items: Array<{
    bookId: string
    title: string
    price: number
    quantity: number
  }>
  totalPrice: number
  paymentMethod: 'direct' | 'online'
  status: string
  createdAt: string
}

export default function OrderSuccessPage({ orderId }: OrderSuccessProps) {
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (orderId) {
      fetchOrder(orderId)
    } else {
      setLoading(false)
    }
  }, [orderId])

  const fetchOrder = async (id: string) => {
    try {
      const { orderService } = await import('services/api')
      const orderData = await orderService.getById(id)
      setOrder(orderData)
    } catch (error) {
      console.error('Error fetching order:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <BookStoreLayout>
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
            <p className="mt-4 text-gray-600">Đang tải thông tin đơn hàng...</p>
          </div>
        </div>
      </BookStoreLayout>
    )
  }

  if (!orderId || !order) {
    return (
      <BookStoreLayout>
        <div className="max-w-4xl mx-auto px-4 py-16">
          <div className="text-center">
            <div className="w-24 h-24 mx-auto mb-8 bg-red-100 rounded-full flex items-center justify-center">
              <svg
                className="w-12 h-12 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Không tìm thấy đơn hàng
            </h1>
            <p className="text-gray-600 mb-8">
              Đơn hàng không tồn tại hoặc đã bị xóa.
            </p>
            <Link href="/bookstore">
              <Button size="lg">Quay lại cửa hàng</Button>
            </Link>
          </div>
        </div>
      </BookStoreLayout>
    )
  }

  return (
    <BookStoreLayout>
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Success Header */}
        <div className="text-center mb-12">
          <div className="w-24 h-24 mx-auto mb-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg
              className="w-12 h-12 text-green-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Đặt hàng thành công!
          </h1>
          <p className="text-xl text-gray-600 mb-2">
            Cảm ơn bạn đã đặt hàng tại IBBook
          </p>
          <p className="text-gray-600">
            Mã đơn hàng:{' '}
            <span className="font-mono font-semibold text-primary-600">
              #{order.id}
            </span>
          </p>
        </div>

        {/* Order Details */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden mb-8">
          <div className="px-6 py-4 bg-gray-50 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              Chi tiết đơn hàng
            </h2>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {/* Customer Info */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">
                  Thông tin khách hàng
                </h3>
                <div className="space-y-2 text-sm">
                  <p>
                    <span className="text-gray-600">Họ tên:</span>{' '}
                    {order.customerName}
                  </p>
                  <p>
                    <span className="text-gray-600">Email:</span> {order.email}
                  </p>
                  <p>
                    <span className="text-gray-600">Điện thoại:</span>{' '}
                    {order.phone}
                  </p>
                </div>
              </div>

              {/* Shipping Info */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">
                  Địa chỉ giao hàng
                </h3>
                <div className="text-sm text-gray-600">
                  <p>{order.address}</p>
                  <p>
                    {order.ward}, {order.district}, {order.city}
                  </p>
                </div>
              </div>
            </div>

            {/* Order Items */}
            <div className="mb-8">
              <h3 className="font-semibold text-gray-900 mb-4">
                Sản phẩm đã đặt
              </h3>
              <div className="space-y-4">
                {order.items.map((item, index) => (
                  <div
                    key={index}
                    className="flex justify-between items-center py-3 border-b border-gray-100 last:border-b-0"
                  >
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">
                        {item.title}
                      </h4>
                      <p className="text-sm text-gray-600">
                        Số lượng: {item.quantity}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {formatNumber(item.price * item.quantity)} ₫
                      </p>
                      <p className="text-sm text-gray-600">
                        {formatNumber(item.price)} ₫ × {item.quantity}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Order Summary */}
            <div className="border-t pt-6">
              <div className="flex justify-between items-center mb-4">
                <span className="text-lg font-semibold">Tổng cộng:</span>
                <span className="text-2xl font-bold text-primary-600">
                  {formatNumber(order.totalPrice)} ₫
                </span>
              </div>
              <div className="flex justify-between items-center text-sm text-gray-600">
                <span>Phương thức thanh toán:</span>
                <span className="font-medium">
                  {order.paymentMethod === 'direct'
                    ? 'Thanh toán trực tiếp'
                    : 'Thanh toán online'}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        <div className="bg-blue-50 rounded-lg p-6 mb-8">
          <h3 className="font-semibold text-blue-900 mb-4">Bước tiếp theo</h3>
          <div className="space-y-3 text-sm text-blue-800">
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold">1</span>
              </div>
              <p>
                Chúng tôi sẽ xác nhận đơn hàng và liên hệ với bạn trong vòng 24
                giờ
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold">2</span>
              </div>
              <p>
                Đơn hàng sẽ được chuẩn bị và giao trong vòng 2-3 ngày làm việc
              </p>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-6 h-6 bg-blue-200 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-xs font-semibold">3</span>
              </div>
              <p>
                File nghe miễn phí sẽ được gửi qua email sau khi xác nhận đơn
                hàng
              </p>
            </div>
          </div>
        </div>

        {/* Contact Info */}
        <div className="bg-gray-50 rounded-lg p-6 mb-8">
          <h3 className="font-semibold text-gray-900 mb-4">Cần hỗ trợ?</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-gray-600 mb-2">Liên hệ trực tiếp:</p>
              <p className="font-medium">Điện thoại: 0949351612</p>
              <p className="font-medium">Email: <EMAIL></p>
            </div>
            <div>
              <p className="text-gray-600 mb-2">Kết nối với chúng tôi:</p>
              <p className="font-medium">Zalo: IBBook</p>
              <p className="font-medium">Facebook: Sách tiếng Anh Hà Nội</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/bookstore">
            <Button appearance="secondary" size="lg">
              Tiếp tục mua sắm
            </Button>
          </Link>
          <Button size="lg" onClick={() => window.print()}>
            In đơn hàng
          </Button>
        </div>
      </div>
    </BookStoreLayout>
  )
}

export const getServerSideProps: GetServerSideProps = async ({ query }) => {
  const orderId = query.orderId as string

  return {
    props: {
      orderId: orderId || null,
    },
  }
}
