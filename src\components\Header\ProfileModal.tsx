import { But<PERSON> } from 'components/Button'
import { Dialog } from 'components/Dialog'
import { toast } from 'components/Toast'
import { useAuth } from 'context/authContext'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'

interface Props {
  trigger: React.ReactNode
  open: boolean
  onOpenChange: (open: boolean) => void
}

const validationSchema = z.object({
  firstName: z.string().min(1, 'Required.'),
  lastName: z.string().min(1, 'Required.'),
})

export const ProfileModal = (props: Props) => {
  const { trigger, open, onOpenChange } = props
  const { user } = useAuth()
  const formInstance = useForm({
    defaultValues: { firstName: user?.firstName, lastName: user?.lastName },
    resolver: zodResolver(validationSchema),
  })
  const { handleSubmit } = formInstance

  const onSubmit = () => {
    onOpenChange(false)
    toast.success({
      title: 'Profile updated',
      message: 'Lorem ipsum dolor sit amet, consectetur.',
    })
  }

  return (
    <>
      <div onClick={() => onOpenChange(true)}>{trigger}</div>
      <Dialog
        isOpen={open}
        onClose={() => onOpenChange(false)}
        title="Edit profile"
        maxWidth="md"
      >
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-5 mb-8">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                First Name
              </label>
              <input
                type="text"
                placeholder="First name"
                defaultValue={user?.firstName}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Last Name
              </label>
              <input
                type="text"
                placeholder="Last name"
                defaultValue={user?.lastName}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
          <div className="flex justify-end space-x-2">
            <Button
              appearance="secondary"
              type="button"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </Button>
            <Button appearance="primary" type="submit">
              Save changes
            </Button>
          </div>
        </form>
      </Dialog>
    </>
  )
}
