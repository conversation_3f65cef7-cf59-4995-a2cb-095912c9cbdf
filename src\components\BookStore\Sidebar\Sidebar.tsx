import Link from 'next/link'

export const Sidebar = () => {
  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 h-full overflow-y-auto">
      {/* Cart Section */}
      <div className="mb-8">
        <h3 className="font-bold text-gray-900 mb-6 text-lg">
          Giỏ hàng của bạn
        </h3>
        <div className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-xl p-8 text-center border border-gray-200">
          <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary-100 to-primary-200 rounded-full flex items-center justify-center">
            <svg
              className="w-10 h-10 text-primary-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
              />
            </svg>
          </div>
          <p className="text-gray-600 text-sm mb-6 leading-relaxed">
            Giỏ hàng trống
          </p>
          <Link href="/bookstore">
            <button className="bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white px-6 py-2 rounded-lg text-sm font-semibold transition-all duration-200 transform hover:scale-105">
              Chọn sách ngay
            </button>
          </Link>
        </div>
      </div>

      {/* Grade Levels */}
      <div className="mb-8">
        <h3 className="font-semibold text-gray-900 mb-4">
          Shop by Grade or CERF level:
        </h3>
        <div className="grid grid-cols-3 gap-2 mb-4">
          {['Pre-K', 'K', '1', '2', '3', '4', '5', '6', '7', '8'].map(
            (grade) => (
              <Link
                key={grade}
                href={`/grade/${grade.toLowerCase()}`}
                className="aspect-square bg-primary-50 hover:bg-primary-100 rounded-lg flex items-center justify-center text-xs font-medium text-primary-700 transition-colors"
              >
                {grade}
              </Link>
            ),
          )}
        </div>

        <div className="grid grid-cols-2 gap-2">
          {[
            { label: 'Starters', color: 'bg-blue-50 text-blue-700' },
            { label: 'Movers', color: 'bg-green-50 text-green-700' },
            { label: 'Flyers', color: 'bg-yellow-50 text-yellow-700' },
            { label: 'A2 (KET)', color: 'bg-purple-50 text-purple-700' },
            { label: 'B1 (PET)', color: 'bg-pink-50 text-pink-700' },
            { label: 'B2 (FCE)', color: 'bg-indigo-50 text-indigo-700' },
            { label: 'C1 (CAE)', color: 'bg-red-50 text-red-700' },
            { label: 'C2 (CPE)', color: 'bg-gray-50 text-gray-700' },
          ].map((level) => (
            <Link
              key={level.label}
              href={`/level/${level.label
                .toLowerCase()
                .replace(/[^a-z0-9]/g, '-')}`}
              className={`p-2 rounded-lg text-xs font-medium text-center transition-colors hover:opacity-80 ${level.color}`}
            >
              {level.label}
            </Link>
          ))}
        </div>

        <Link
          href="/ielts"
          className="block w-full mt-2 p-2 bg-orange-50 text-orange-700 rounded-lg text-xs font-medium text-center hover:bg-orange-100 transition-colors"
        >
          IELTS
        </Link>
      </div>

      {/* Information Links */}
      <div className="mb-8">
        <h3 className="font-semibold text-gray-900 mb-4">THÔNG TIN</h3>
        <ul className="space-y-2 text-sm">
          <li>
            <Link
              href="/about"
              className="text-gray-600 hover:text-primary-500"
            >
              • Sách gáy xoắn là gì?
            </Link>
          </li>
          <li>
            <Link href="/blog" className="text-gray-600 hover:text-primary-500">
              • Blog chia sẻ
            </Link>
          </li>
          <li>
            <Link
              href="/shipping"
              className="text-gray-600 hover:text-primary-500"
            >
              • Giao toàn quốc
            </Link>
          </li>
          <li>
            <Link
              href="/guide"
              className="text-gray-600 hover:text-primary-500"
            >
              • Hướng dẫn
            </Link>
          </li>
          <li>
            <Link
              href="/discount"
              className="text-gray-600 hover:text-primary-500"
            >
              • Mã giảm giá
            </Link>
          </li>
          <li>
            <Link
              href="/points"
              className="text-gray-600 hover:text-primary-500"
            >
              • Điểm thưởng
            </Link>
          </li>
          <li>
            <Link
              href="/return"
              className="text-gray-600 hover:text-primary-500"
            >
              • Đổi/Trả/Hoàn tiền
            </Link>
          </li>
        </ul>
      </div>

      {/* Categories List 1 */}
      <div className="mb-8">
        <h3 className="font-semibold text-gray-900 mb-4">DANH SÁCH 1</h3>
        <ul className="space-y-2 text-sm">
          <li>
            <Link
              href="/early-years"
              className="text-gray-600 hover:text-primary-500"
            >
              • Early Years (3–5 years old)
            </Link>
          </li>
          <li>
            <Link
              href="/primary"
              className="text-gray-600 hover:text-primary-500"
            >
              • Primary
            </Link>
          </li>
          <li>
            <Link
              href="/secondary"
              className="text-gray-600 hover:text-primary-500"
            >
              • Secondary
            </Link>
          </li>
          <li>
            <Link
              href="/adults"
              className="text-gray-600 hover:text-primary-500"
            >
              • Adults courses
            </Link>
          </li>
          <li>
            <Link href="/esp" className="text-gray-600 hover:text-primary-500">
              • ESP
            </Link>
          </li>
          <li>
            <Link
              href="/iq-books"
              className="text-gray-600 hover:text-primary-500 font-semibold"
            >
              • IQ Books
            </Link>
          </li>
          <li>
            <Link
              href="/french"
              className="text-gray-600 hover:text-primary-500 font-semibold"
            >
              • Français
            </Link>
          </li>
        </ul>
      </div>

      {/* Categories List 2 */}
      <div className="mb-8">
        <h3 className="font-semibold text-gray-900 mb-4">DANH SÁCH 2</h3>
        <ul className="space-y-2 text-sm">
          <li>
            <Link
              href="/study-abroad"
              className="text-gray-600 hover:text-primary-500"
            >
              • Tủ sách du học
            </Link>
          </li>
          <li>
            <Link
              href="/asean"
              className="text-gray-600 hover:text-primary-500 font-semibold"
            >
              • ASEAN Scholarships
            </Link>
          </li>
          <li>
            <Link
              href="/grammar"
              className="text-gray-600 hover:text-primary-500"
            >
              • Grammar
            </Link>
          </li>
          <li>
            <Link
              href="/exam-prep"
              className="text-gray-600 hover:text-primary-500"
            >
              • Exam Preparation
            </Link>
          </li>
          <li>
            <Link href="/ib" className="text-gray-600 hover:text-primary-500">
              • IB Programme
            </Link>
          </li>
          <li>
            <Link
              href="/a-level"
              className="text-gray-600 hover:text-primary-500"
            >
              • A/AS Level
            </Link>
          </li>
          <li>
            <Link href="/emg" className="text-gray-600 hover:text-primary-500">
              • Chương trình tích hợp EMG
            </Link>
          </li>
        </ul>
      </div>

      {/* Search Everything */}
      <div className="mb-8">
        <h3 className="font-semibold text-gray-900 mb-4">TÌM MỌI THỨ</h3>
        <p className="text-sm text-gray-600 mb-4">
          LUÔN GỬI MIỄN PHÍ FILE NGHE CHO <strong>KHÁCH HÀNG CỦA SHOP</strong>.
        </p>
        <div className="flex space-x-2">
          <Link
            href="/zalo"
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm"
          >
            Zalo
          </Link>
        </div>
      </div>
    </div>
  )
}
