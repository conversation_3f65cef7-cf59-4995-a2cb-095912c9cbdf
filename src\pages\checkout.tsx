import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { BookStoreLayout } from 'components/BookStore/Layout'
import { useCartContext } from 'context/cart'
import { formatNumber } from 'utils/number'
import { Button } from 'components/Button'
import { FormInput } from 'components/FormInput'
import toast from 'react-hot-toast'

// Validation schema
const checkoutSchema = z.object({
  customerName: z.string().min(2, 'Tên phải có ít nhất 2 ký tự'),
  email: z.string().email('Email không hợp lệ'),
  phone: z.string().min(10, 'Số điện thoại phải có ít nhất 10 số'),
  address: z.string().min(10, 'Địa chỉ phải có ít nhất 10 ký tự'),
  city: z.string().min(2, '<PERSON><PERSON>à<PERSON> phố không được để trống'),
  district: z.string().min(2, 'Quận/Huyện không được để trống'),
  ward: z.string().min(2, 'Phường/Xã không được để trống'),
  notes: z.string().optional(),
})

type CheckoutFormData = z.infer<typeof checkoutSchema>

export default function CheckoutPage() {
  const router = useRouter()
  const { items, totalPrice, clearCart } = useCartContext()
  const [paymentMethod, setPaymentMethod] = useState<'direct' | 'online'>(
    'direct',
  )
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
  })

  // Redirect if cart is empty
  useEffect(() => {
    if (items.length === 0) {
      router.push('/cart')
    }
  }, [items, router])

  const onSubmit = async (data: CheckoutFormData) => {
    setIsSubmitting(true)

    try {
      const orderData = {
        customerName: data.customerName,
        email: data.email,
        phone: data.phone,
        address: data.address,
        city: data.city,
        district: data.district,
        ward: data.ward,
        notes: data.notes,
        items: items.map((item) => ({
          bookId: item.book.id,
          title: item.book.title,
          price: item.book.price,
          quantity: item.quantity,
        })),
        totalPrice,
        paymentMethod,
      }

      if (paymentMethod === 'direct') {
        // Direct payment - create order and show success
        const { orderService } = await import('services/api')
        const order = await orderService.create(orderData)

        clearCart()
        toast.success('Đặt hàng thành công!')
        router.push(`/order-success?orderId=${order.id}`)
      } else {
        // Online payment - create order first, then redirect to payment
        const { orderService, paymentService } = await import('services/api')
        const order = await orderService.create(orderData)

        // Create payment URL
        const paymentData = {
          orderId: order.id,
          amount: totalPrice,
          returnUrl: `${window.location.origin}/payment/return`,
          cancelUrl: `${window.location.origin}/payment/cancel?orderId=${order.id}`,
        }

        const { paymentUrl } = await paymentService.create(paymentData)

        // Clear cart before redirecting
        clearCart()

        // Redirect to payment gateway
        window.location.href = paymentUrl
      }
    } catch (error) {
      console.error('Checkout error:', error)
      toast.error(
        error instanceof Error
          ? error.message
          : 'Có lỗi xảy ra. Vui lòng thử lại.',
      )
    } finally {
      setIsSubmitting(false)
    }
  }

  if (items.length === 0) {
    return null // Will redirect
  }

  return (
    <BookStoreLayout>
      <div className="max-w-6xl mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Thanh toán</h1>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Customer Information */}
            <div className="lg:col-span-2 space-y-8">
              {/* Contact Information */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Thông tin liên hệ
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <FormInput
                    label="Họ và tên *"
                    {...register('customerName')}
                    error={errors.customerName?.message}
                  />
                  <FormInput
                    label="Email *"
                    type="email"
                    {...register('email')}
                    error={errors.email?.message}
                  />
                  <FormInput
                    label="Số điện thoại *"
                    {...register('phone')}
                    error={errors.phone?.message}
                  />
                </div>
              </div>

              {/* Shipping Address */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Địa chỉ giao hàng
                </h2>
                <div className="space-y-6">
                  <FormInput
                    label="Địa chỉ *"
                    {...register('address')}
                    error={errors.address?.message}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <FormInput
                      label="Thành phố *"
                      {...register('city')}
                      error={errors.city?.message}
                    />
                    <FormInput
                      label="Quận/Huyện *"
                      {...register('district')}
                      error={errors.district?.message}
                    />
                    <FormInput
                      label="Phường/Xã *"
                      {...register('ward')}
                      error={errors.ward?.message}
                    />
                  </div>
                  <FormInput
                    label="Ghi chú (tùy chọn)"
                    {...register('notes')}
                    placeholder="Ghi chú thêm về đơn hàng..."
                  />
                </div>
              </div>

              {/* Payment Method */}
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Phương thức thanh toán
                </h2>
                <div className="space-y-4">
                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="direct"
                      checked={paymentMethod === 'direct'}
                      onChange={(e) =>
                        setPaymentMethod(e.target.value as 'direct')
                      }
                      className="mt-1"
                    />
                    <div>
                      <div className="font-medium">Thanh toán trực tiếp</div>
                      <div className="text-sm text-gray-600">
                        Thanh toán khi nhận hàng (COD) hoặc chuyển khoản trực
                        tiếp
                      </div>
                    </div>
                  </label>
                  <label className="flex items-start space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="paymentMethod"
                      value="online"
                      checked={paymentMethod === 'online'}
                      onChange={(e) =>
                        setPaymentMethod(e.target.value as 'online')
                      }
                      className="mt-1"
                    />
                    <div>
                      <div className="font-medium">Thanh toán online</div>
                      <div className="text-sm text-gray-600">
                        Thanh toán qua 9Pay - Hỗ trợ thẻ ATM, Visa, MasterCard
                      </div>
                    </div>
                  </label>
                </div>
              </div>
            </div>

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-4">
                <h2 className="text-xl font-semibold text-gray-900 mb-6">
                  Đơn hàng của bạn
                </h2>

                {/* Order Items */}
                <div className="space-y-4 mb-6">
                  {items.map((item) => (
                    <div
                      key={item.book.id}
                      className="flex items-center space-x-3"
                    >
                      <img
                        src={item.book.image}
                        alt={item.book.title}
                        className="w-12 h-16 object-cover rounded"
                      />
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {item.book.title}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {formatNumber(item.book.price)} ₫ × {item.quantity}
                        </p>
                      </div>
                      <div className="text-sm font-medium">
                        {formatNumber(item.book.price * item.quantity)} ₫
                      </div>
                    </div>
                  ))}
                </div>

                {/* Order Total */}
                <div className="space-y-3 border-t pt-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Tạm tính</span>
                    <span className="font-medium">
                      {formatNumber(totalPrice)} ₫
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Phí vận chuyển</span>
                    <span className="font-medium text-green-600">Miễn phí</span>
                  </div>
                  <div className="flex justify-between text-lg font-semibold border-t pt-3">
                    <span>Tổng cộng</span>
                    <span className="text-primary-600">
                      {formatNumber(totalPrice)} ₫
                    </span>
                  </div>
                </div>

                {/* Submit Button */}
                <Button
                  type="submit"
                  size="lg"
                  className="w-full mt-6"
                  disabled={isSubmitting}
                >
                  {isSubmitting
                    ? 'Đang xử lý...'
                    : paymentMethod === 'direct'
                    ? 'Đặt hàng'
                    : 'Thanh toán ngay'}
                </Button>

                <div className="mt-4 text-center text-sm text-gray-600">
                  Bằng cách đặt hàng, bạn đồng ý với{' '}
                  <a
                    href="/terms"
                    className="text-primary-600 hover:text-primary-700"
                  >
                    Điều khoản dịch vụ
                  </a>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </BookStoreLayout>
  )
}
