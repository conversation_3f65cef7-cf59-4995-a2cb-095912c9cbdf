import React, { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/router'

import { useCartContext } from 'context/cart'
import { useAuth } from 'context/authContext'

export const Header = () => {
  const [searchTerm, setSearchTerm] = useState('')
  const [showUserMenu, setShowUserMenu] = useState(false)
  const userMenuRef = useRef<HTMLDivElement>(null)
  const { totalItems } = useCartContext()
  const { user, isAuthenticated, logout } = useAuth()

  const router = useRouter()

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        userMenuRef.current &&
        !userMenuRef.current.contains(event.target as Node)
      ) {
        setShowUserMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm.trim())}`)
    }
  }

  return (
    <header className="bg-white shadow-lg border-b border-gray-100 sticky top-0 z-50 backdrop-blur-sm">
      {/* Top Bar */}
      <div className="bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3">
        <div className="max-w-7xl mx-auto px-4 flex justify-between items-center text-sm">
          <div className="flex items-center space-x-8">
            <span className="flex items-center">
              <svg
                className="w-4 h-4 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z" />
              </svg>
              0908540198
            </span>
            <Link
              href="/pdf"
              className="hover:text-primary-100 transition-colors duration-200 font-medium"
            >
              PDF
            </Link>
            <Link
              href="/erc"
              className="hover:text-primary-100 transition-colors duration-200 font-medium"
            >
              ERC
            </Link>
            <Link
              href="/audio"
              className="hover:text-primary-100 transition-colors duration-200 font-medium"
            >
              AUDIO
            </Link>
          </div>
          <div className="flex items-center space-x-8">
            <Link
              href="/contact"
              className="hover:text-primary-100 transition-colors duration-200 font-medium"
            >
              Liên hệ
            </Link>
            <Link
              href="/catalog"
              className="hover:text-primary-100 transition-colors duration-200 font-medium"
            >
              Catalog
            </Link>
            <Link
              href="/faq"
              className="hover:text-primary-100 transition-colors duration-200 font-medium"
            >
              FAQ
            </Link>
            {isAuthenticated ? (
              <div className="relative" ref={userMenuRef}>
                <button
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-2 hover:text-primary-100 transition-colors duration-200 font-medium"
                >
                  <span>👤 {user?.name || user?.email}</span>
                  <svg
                    className="w-4 h-4"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
                {showUserMenu && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                    <div className="px-4 py-2 text-sm text-gray-700 border-b">
                      <div className="font-medium">
                        {user?.name || user?.email}
                      </div>
                      <div className="text-gray-500">{user?.email}</div>
                      {user?.phone && (
                        <div className="text-gray-500">{user?.phone}</div>
                      )}
                    </div>
                    {user?.role === 'admin' && (
                      <Link
                        href="/admin/dashboard"
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setShowUserMenu(false)}
                      >
                        🔧 Admin Panel
                      </Link>
                    )}
                    <Link
                      href="/account"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      👤 Tài khoản
                    </Link>
                    <Link
                      href="/orders"
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => setShowUserMenu(false)}
                    >
                      📦 Đơn hàng
                    </Link>
                    <button
                      onClick={() => {
                        logout()
                        setShowUserMenu(false)
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                    >
                      🚪 Đăng xuất
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href="/login"
                className="hover:text-primary-100 transition-colors duration-200 font-medium"
              >
                🔑 Đăng nhập
              </Link>
            )}
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex items-center group">
            <div className="text-3xl font-bold bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent group-hover:from-primary-700 group-hover:to-primary-800 transition-all duration-200">
              IBBook
            </div>
          </Link>

          {/* Search Bar */}
          <div className="flex-1 max-w-2xl mx-8">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="Tìm kiếm sách..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-6 py-4 border-2 border-gray-200 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-200 bg-gray-50 focus:bg-white shadow-sm"
              />
              <button
                type="submit"
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-primary-500 hover:bg-primary-600 text-white p-2 rounded-lg transition-colors duration-200"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </button>
            </form>
          </div>

          {/* Cart */}
          <div className="flex items-center">
            <Link
              href="/cart"
              className="relative group bg-gray-100 hover:bg-primary-50 p-4 rounded-xl transition-all duration-200 transform hover:scale-105"
            >
              <svg
                className="w-7 h-7 text-gray-600 group-hover:text-primary-600 transition-colors duration-200"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"
                />
              </svg>
              {totalItems > 0 && (
                <span className="absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full w-6 h-6 flex items-center justify-center shadow-lg animate-pulse">
                  {totalItems}
                </span>
              )}
            </Link>
          </div>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="bg-gradient-to-r from-gray-50 to-gray-100 border-t border-gray-200">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center space-x-8 py-4 overflow-x-auto">
            <Link
              href="/cambridge"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              Cambridge
            </Link>
            <Link
              href="/checkpoint"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              Checkpoint
            </Link>
            <Link
              href="/igcse"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              IGCSE
            </Link>
            <Link
              href="/cerf"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              CERF
            </Link>
            <Link
              href="/ielts"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              IELTS
            </Link>
            <Link
              href="/exam"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              Luyện thi
            </Link>
            <Link
              href="/skills"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              Kỹ năng
            </Link>
            <Link
              href="/publisher"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              Nhà xuất bản
            </Link>
            <Link
              href="/esp"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              ESP
            </Link>
            <Link
              href="/singapore"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              Singapore
            </Link>
            <Link
              href="/american"
              className="whitespace-nowrap text-sm font-medium text-gray-700 hover:text-primary-500 transition-colors duration-200"
            >
              American
            </Link>
          </div>
        </div>
      </nav>
    </header>
  )
}
