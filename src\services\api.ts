import axios, { AxiosError, InternalAxiosRequestConfig } from 'axios'
import { handleApiError } from './apiErrorHandler'
import { localStorageUtils } from '../utils/localStorage'

const getToken = (): string | null => {
  return localStorageUtils.getAccessToken()
}

// Create axios instance with default config
const api = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = getToken() // Use the getToken function
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error: AxiosError) => {
    return Promise.reject(error)
  },
)

// Response interceptor
api.interceptors.response.use((response) => response, handleApiError)

// Mock services for now
export const orderService = {
  create: async (data: any) => {
    console.log('Creating order:', data)
    return { id: 'mock-order-id', ...data }
  },
  getById: async (id: string) => {
    console.log('Getting order:', id)
    return {
      id,
      customerName: 'Mock Customer',
      email: '<EMAIL>',
      phone: '0901234567',
      address: '123 Mock Street',
      city: 'Mock City',
      district: 'Mock District',
      ward: 'Mock Ward',
      status: 'completed',
      paymentMethod: 'direct' as const,
      totalPrice: 100000,
      createdAt: new Date().toISOString(),
      items: [
        {
          bookId: '1',
          title: 'Mock Book',
          price: 100000,
          quantity: 1,
        },
      ],
    }
  },
  updateStatus: async (orderId: string, status: string) => {
    console.log('Updating order status:', orderId, status)
    return { success: true }
  },
}

export const paymentService = {
  create: async (data: any) => {
    console.log('Creating payment:', data)
    return { paymentUrl: 'https://mock-payment-url.com' }
  },
  verifyReturn: async (params: any) => {
    console.log('Verifying payment:', params)
    return { success: true, orderId: 'mock-order-id' }
  },
  verify: async (orderId: string, transactionId: string) => {
    console.log('Verifying payment:', orderId, transactionId)
    return { success: true, orderId }
  },
}

export default api
